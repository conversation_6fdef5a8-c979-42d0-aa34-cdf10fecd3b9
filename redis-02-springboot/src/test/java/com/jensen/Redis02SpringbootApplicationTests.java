package com.jensen;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jensen.pojo.User;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;

@SpringBootTest
class Redis02SpringbootApplicationTests {

    @Autowired
    private RedisTemplate redisTemplate;

    @Test
    void contextLoads() {

        // redisTemplate  操作不同的数据类型，API和我们Redis的指令是一样的
        // opsForValue 操作字符串 类似String
        // opsForList  操作List  类似List
        // opsForSet   操作Set   类似Set
        // opsForHash  操作Hash  类似Hash
        // opsForZSet  操作ZSet  类似ZSet
        // opsForGeo   操作Geo   类似Geospatial
        // opsForHyperLogLog    操作HyperLogLog   类似HyperLogLog

        // 除了基本的操作，我们常用的方法都可以直接通过redisTemplate操作，比如事务，和基本的CRUD

        // 获取Redis的连接对象
        // RedisConnection connection = redisTemplate.getConnectionFactory().getConnection();

        redisTemplate.opsForValue().set("mykey","jensen");
        System.out.println(redisTemplate.opsForValue().get("mykey"));
    }

    @Test
    public void test() throws JsonProcessingException {
        // 真实的开发一般都使用json来传递对象
        User user = new User("jensen", 18);
        String jsonUser = new ObjectMapper().writeValueAsString(user);
        redisTemplate.opsForValue().set("user",jsonUser);
        System.out.println(redisTemplate.opsForValue().get("user"));
    }

}
